import {
  <PERSON><PERSON><PERSON>,
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  Request,
  Injectable,
  UnauthorizedException,
} from "@nestjs/common";
import { Request as ExpressRequest } from "express";
import { JwtModule, JwtService } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { PassportModule } from "@nestjs/passport";
import { AuthGuard } from "@nestjs/passport";
import { Strategy, ExtractJwt } from "passport-jwt";
import { PassportStrategy } from "@nestjs/passport";

// Mock User interface
interface MockUser {
  id: string;
  email: string;
  name: string;
}

// Mock Users Service
@Injectable()
export class MockUsersService {
  private users: MockUser[] = [
    { id: "1", email: "<EMAIL>", name: "Test User" },
    { id: "2", email: "<EMAIL>", name: "Admin User" },
  ];

  async findByEmail(email: string): Promise<MockUser | null> {
    return this.users.find((user) => user.email === email) || null;
  }

  async create(email: string, name: string): Promise<MockUser> {
    const newUser: MockUser = {
      id: (this.users.length + 1).toString(),
      email,
      name,
    };
    this.users.push(newUser);
    return newUser;
  }
}

// JWT Strategy
@Injectable()
export class MockJwtStrategy extends PassportStrategy(Strategy) {
  constructor(configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get("JWT_SECRET", "supersecret"),
    });
  }

  async validate(payload: any) {
    return { id: payload.sub, email: payload.email, name: payload.name };
  }
}

// Mock Auth Service
@Injectable()
export class MockAuthService {
  constructor(
    private usersService: MockUsersService,
    private jwtService: JwtService
  ) {}

  async validateUser(
    email: string,
    password: string
  ): Promise<MockUser | null> {
    // Mock password validation - accept any password for demo
    const user = await this.usersService.findByEmail(email);
    if (user) {
      return user;
    }
    return null;
  }

  async login(email: string, password: string) {
    const user = await this.validateUser(email, password);
    if (!user) {
      throw new UnauthorizedException("Invalid credentials");
    }

    const payload = { email: user.email, sub: user.id, name: user.name };
    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.jwtService.sign(payload, { expiresIn: "7d" });

    return {
      user,
      accessToken,
      refreshToken,
    };
  }

  async register(email: string, password: string, name?: string) {
    // Check if user already exists
    const existingUser = await this.usersService.findByEmail(email);
    if (existingUser) {
      throw new UnauthorizedException("User already exists");
    }

    const user = await this.usersService.create(email, name || "New User");
    const payload = { email: user.email, sub: user.id, name: user.name };
    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.jwtService.sign(payload, { expiresIn: "7d" });

    return {
      user,
      accessToken,
      refreshToken,
    };
  }
}

// Auth Controller
@Controller("auth")
export class MockAuthController {
  constructor(private authService: MockAuthService) {}

  @Post("login")
  async login(@Body() body: { email: string; password: string }) {
    return this.authService.login(body.email, body.password);
  }

  @Post("register")
  async register(
    @Body() body: { email: string; password: string; name?: string }
  ) {
    return this.authService.register(body.email, body.password, body.name);
  }

  @Post("refresh")
  async refresh(@Body() body: { refreshToken: string }) {
    // Mock refresh - just return the same token for demo
    return { accessToken: body.refreshToken };
  }

  @Get("me")
  @UseGuards(AuthGuard("jwt"))
  async getProfile(@Request() req: ExpressRequest & { user: any }) {
    return req.user;
  }

  @Post("logout")
  async logout() {
    return { message: "Logged out successfully" };
  }
}

// Users Controller
@Controller("users")
export class MockUsersController {
  constructor(private usersService: MockUsersService) {}

  @Get("me")
  @UseGuards(AuthGuard("jwt"))
  async getCurrentUser(@Request() req: ExpressRequest & { user: any }) {
    return req.user;
  }
}

// Mock Auth Module
@Module({
  imports: [
    ConfigModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get("JWT_SECRET", "supersecret"),
        signOptions: { expiresIn: "15m" },
      }),
    }),
  ],
  providers: [MockAuthService, MockUsersService, MockJwtStrategy],
  controllers: [MockAuthController, MockUsersController],
  exports: [MockAuthService],
})
export class MockAuthModule {}
